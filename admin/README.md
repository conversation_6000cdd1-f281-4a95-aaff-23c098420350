法人实体管理
数据库
spring.datasource.hikari.jdbc-url=******************************************************************************************
spring.datasource.hikari.username=root
spring.datasource.hikari.password=root
表（T_TAX_SUBJECT，T_TAX_REPORT_INFO，T_COMPANY， T_TAX_SUB_AUTH_INFO）

from T_TAX_SUBJECT a left join T_TAX_REPORT_INFO b on a.TAX_SUB_ID=b.TAX_SUB_ID and SW_WORKER_STATUS='NORMAL'
left join T_COMPANY c on a.COMP_ID=c.COMP_ID
left join T_TAX_SUB_AUTH_INFO d on a.TAX_SUB_ID=d.TAX_SUB_ID


详情页

有这二个参数但是没有进行特殊处理

表（   T_TAX_SUB_AUTH_INFO， T_TAX_SUBJECT， T_COMPANY）
FROM
T_TAX_SUB_AUTH_INFO a
LEFT JOIN T_TAX_SUBJECT b
on a.TAX_SUB_ID = b.TAX_SUB_ID
LEFT JOIN T_COMPANY c
on b.COMP_ID = c.COMP_ID


法人实体管理导出

表（T_TAX_SUBJECT，T_TAX_REPORT_INFO，T_COMPANY， T_TAX_SUB_AUTH_INFO）
from T_TAX_SUBJECT a left join T_TAX_REPORT_INFO b on a.TAX_SUB_ID=b.TAX_SUB_ID and SW_WORKER_STATUS='NORMAL'
left join T_COMPANY c on a.COMP_ID=c.COMP_ID
left join T_TAX_SUB_AUTH_INFO d on a.TAX_SUB_ID=d.TAX_SUB_ID


/////////////////////////////////////////////////////////////////////////////////////////////////////////////
基础设置：参保方案（查询）

表（T_BASE_INSURED_PROJECT，T_DICT，T_BASE_INSURED_DETAIL）

from T_BASE_INSURED_PROJECT a left join T_DICT b on a.INSURED_CITY=b.DICT_CODE left join T_BASE_INSURED_DETAIL c on a.ID=c.BASE_INSURED_ID


参保方案（新增）
表(T_DICT,T_BASE_INSURED_PROJECT,)

参保方案（新增-查询城市）
表(T_DICT)


参保详情（查询）

表（T_BASE_INSURED_PROJECT， T_BASE_INSURED_DETAIL， T_DICT）
from T_BASE_INSURED_PROJECT a right join T_BASE_INSURED_DETAIL b on a.ID=b.BASE_INSURED_ID
left join T_DICT c on a.INSURED_CITY=c.DICT_CODE

参保详情（新增）

表（T_BASE_INSURED_PROJECT，T_BASE_INSURED_DETAIL，）

参保详情（导出）

表（ T_BASE_INSURED_PROJECT， T_DICT，T_BASE_INSURED_DETAIL ）
from T_BASE_INSURED_PROJECT a left join T_DICT b on a.INSURED_CITY=b.DICT_CODE left join T_BASE_INSURED_DETAIL c on a.ID=c.BASE_INSURED_ID

参保详情（删除）
表（  T_BASE_INSURED_DETAIL）



### 注意
authorityRpcService2    rpc查找不到

仓库寻不到的服务：
"lanmaoly-contract-app"

### 商户管理相关表

156连接；merchant数据库：

1.商户列表查询：t_merchant表；t_business表；t_merchant_business_instance表

2.商户详情：t_merchant_business_instance表；t_merchant_app 表；t_merchant表；t_merchant_user表；t_dept表；t_merchant_member表；t_user表；

不确定表：
t_merchant_init 表：存储了商户id和app的code码

### 数据统计相关表
xst_subtax连接；xst_subtax数据库：

t_merchant_fee表（商户人数统计）+    t_taxsub_fee表(纳税人号)   +	 t_merchant（商户名name）

t_taxsub_fee表（纳税计费统计/扣缴义务人统计）	 +	 t_merchant（商户名name）/？（审核状态）
