package com.olading.kxy.admin.domain.model;

import com.olading.kxy.admin.application.commandResult.AppendBaseInsuredDetailCommand;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;

@RunWith(Parameterized.class)
public class BaseInsuredDetailTest {

    @Parameterized.Parameters
    public static Collection<Object[]> data() {
        return Arrays.asList(new Object[][]{
                {"2", "1", "unemployment", "UNEMPLOYMENT_INSURANCE", "100", "10", "0.01", "100", "ROUND_UNTIL_FEN", "0.1", "100", "ROUND_UNTIL_FEN", null},
                {"1", "", "2", "2", "200", "20", "0.02", "200", "2", "0.11", "200", "2", "参保id不能为空"},
                {"3", "1", "", "3", "300", "30", "0.03", "300", "3", "0.12", "300", "3", "参保项目类型不能为空"},
                {"3", "2", "3", "", "300", "30", "0.03", "300", "3", "0.12", "300", "3", "参保类型不能为空"},
                {"4", "4", "3", "4", "-1", "40", "0.04", "400", "4", "0.13", "400", "4", "基数上限不能小于0,上限不能小于下限,基数上限不能超过100000000"}
                , {
                "5", "5", "5", "10", "500", "-1", "0.05", "500", "5", "0.14", "500", "5", "基数下限不能小于0，下限不能大于上限，基数下限不能超过10000"
        }
                , {
                "1", "1", "1", "1", "500", "10", "-1", "600", "4", "0.15", "600", "6", "个人比例不能小于0或大于100%"
        }
                , {
                "1", "1", "1", "1", "500", "10", "0.05", "-1", "4", "0.15", "600", "6", "个人固定金额不能小于0大于1000000"
        }
                , {
                "1", "1", "1", "1", "500", "10", "0.05", "600", "4", "-1", "600", "6", "企业比例不能小于0或大于100%"
        }
                , {
                "1", "1", "1", "1", "500", "10", "0.05", "600", "4", "0.16", "-1", "6", "企业固定金额不能小于0和大于1000000"
        }
                , {
                "1", "1", "1", "1", "500", "10", "0.05", "600", "4", "0.16", "600", "", "企业小数位规则不能为空"
        }
                , {
                "1", "1", "1", "1", "500", "10", "0.05", "600", "", "0.16", "600", "6", "个人小数位规则不能为空"
        }
                //添加更多数据行,每行代表一个独立的测试用例
        });
    }

    private String id;
    private String projectId;
    private String insuredProjectType;
    private String insuranceType;
    private String baseNumberMax;
    private String baseNumberMin;
    private String personScale;
    private String personFixedAmount;
    private String personMantissaRule;
    private String compScale;
    private String compFixedAmount;
    private String compMantissaRule;
    private String expectedExceptionMessage;

    public BaseInsuredDetailTest(String id, String projectId, String insuredProjectType, String insuranceType,
                                 String baseNumberMax, String baseNumberMin, String personScale,
                                 String personFixedAmount, String personMantissaRule, String compScale,
                                 String compFixedAmount, String compMantissaRule, String expectedExceptionMessage) {
        this.id = id;
        this.projectId = projectId;
        this.insuredProjectType = insuredProjectType;
        this.insuranceType = insuranceType;
        this.baseNumberMax = baseNumberMax;
        this.baseNumberMin = baseNumberMin;
        this.personScale = personScale;
        this.personFixedAmount = personFixedAmount;
        this.personMantissaRule = personMantissaRule;
        this.compScale = compScale;
        this.compFixedAmount = compFixedAmount;
        this.compMantissaRule = compMantissaRule;
        this.expectedExceptionMessage = expectedExceptionMessage;

    }

    @Test
    public void testBaseInsuredDetailConstructor() {
        AppendBaseInsuredDetailCommand command = new AppendBaseInsuredDetailCommand();
        command.projectId = projectId;
        command.baseInsuredDetail = new BaseInsuredDetail();
        command.baseInsuredDetail.id = id;
        command.baseInsuredDetail.baseInsuredId = projectId;
        command.baseInsuredDetail.insuredProjectType = insuredProjectType;
        command.baseInsuredDetail.insuranceType = insuranceType;
        command.baseInsuredDetail.baseNumberMax = new BigDecimal(baseNumberMax);
        command.baseInsuredDetail.baseNumberMin = new BigDecimal(baseNumberMin);
        command.baseInsuredDetail.personScale = new BigDecimal(personScale);
        command.baseInsuredDetail.personFixedAmount = new BigDecimal(personFixedAmount);
        command.baseInsuredDetail.personMantissaRule = personMantissaRule;
        command.baseInsuredDetail.compScale = new BigDecimal(compScale);
        command.baseInsuredDetail.compFixedAmount = new BigDecimal(compFixedAmount);
        command.baseInsuredDetail.compMantissaRule = compMantissaRule;

        try {
            new BaseInsuredDetail(id, command);
            Assert.assertEquals("id", id, command.baseInsuredDetail.id);
            Assert.assertEquals("projectId", projectId, command.baseInsuredDetail.baseInsuredId);
            Assert.assertEquals("insuredProjectType", insuredProjectType, command.baseInsuredDetail.insuredProjectType);
            Assert.assertEquals("insuranceType", insuranceType, command.baseInsuredDetail.insuranceType);
            Assert.assertEquals("baseNumberMax", new BigDecimal(baseNumberMax), command.baseInsuredDetail.baseNumberMax);
            Assert.assertEquals("baseNumberMin", new BigDecimal(baseNumberMin), command.baseInsuredDetail.baseNumberMin);
            Assert.assertEquals("personScale", new BigDecimal(personScale), command.baseInsuredDetail.personScale);
            Assert.assertEquals("personFixedAmount", new BigDecimal(personFixedAmount), command.baseInsuredDetail.personFixedAmount);
            Assert.assertEquals("personMantissaRule", personMantissaRule, command.baseInsuredDetail.personMantissaRule);
            Assert.assertEquals("compScale", new BigDecimal(compScale), command.baseInsuredDetail.compScale);
            Assert.assertEquals("compFixedAmount", new BigDecimal(compFixedAmount), command.baseInsuredDetail.compFixedAmount);
            Assert.assertEquals("compMantissaRule", compMantissaRule, command.baseInsuredDetail.compMantissaRule);
        } catch (Exception e) {
            Assert.assertEquals("抛出的异常消息与预期不一致。", expectedExceptionMessage, e.getMessage());
        }

    }

}
