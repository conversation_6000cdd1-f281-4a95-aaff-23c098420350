package com.olading.kxy.admin.domain.model;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DomainServicesTest {

    /*    79b87b4d-2264-4244-b432-1ff8fe1bb742
                KXY2021081300002
        00003948
                711100
                71101*/


    public static TaiLongUserInfo getKXYUserInfo() {
        TaiLongUserInfo taiLongUserInfo = new TaiLongUserInfo();
        taiLongUserInfo.employeeId = "KXY2021081300002";
        taiLongUserInfo.kxyProtocolId = "00003948";
        taiLongUserInfo.blngInstNo = "711100";
        taiLongUserInfo.binInstNo = "71101";
        return taiLongUserInfo;
    }

    @Test
    void testLogin() {
        String tokenId = "79b87b4d-2264-4244-b432-1ff8fe1bb742";
        TaiLongUserInfo taiLongUserInfo = getKXYUserInfo();
        Token token = DomainServices.login(tokenId, 1000, null, taiLongUserInfo);
        Assert.assertEquals("KXY2021081300002", token.taiLongUserInfo.employeeId);

        //case no existed kxy info
        try {
            DomainServices.login(tokenId, 1000, null, null);
        } catch (Exception e) {
            Assert.assertEquals("不支持的验证类型", e.getMessage());
        }
    }

    @Test
    void testAuth() {
//KXY   token a-b-c-d 1712907520 all HttpServletRequest request

        String type = "KXY";
        Token token = new Token();
        token.id = "a-b-c-d";
        token.taiLongUserInfo = getKXYUserInfo();
        token.expiresAt = 1712907520;
        token.allowedMerchantIds = new ArrayList<>();
        DomainServices.auth(type, token, null);
        Assert.assertEquals("KXY2021081300002", token.taiLongUserInfo.employeeId);

        try {
            DomainServices.auth("xxx", token, null);
        } catch (Exception e) {
            Assert.assertEquals("不支持的授权类型", e.getMessage());
        }

    }

    @Test
    void testAuthErrToken() {

        try {
            DomainServices.auth("KXY", null, null);
        } catch (Exception e) {
            Assert.assertEquals("未找到登录信息", e.getMessage());
        }
    }

    @Test
    void testAuthErrExpired() {
        Token token = new Token();
        token.id = "a-b-c-d";
        token.taiLongUserInfo = getKXYUserInfo();
        token.expiresAt = 1;
        token.allowedMerchantIds = new ArrayList<>();
        try {
            DomainServices.auth("KXY", token, null);
        } catch (Exception e) {
            Assert.assertEquals("当前登录已过期", e.getMessage());
        }
    }

    @Test
    void testAuthErrKxyUserInfo() {
        Token token = new Token();
        token.id = "a-b-c-d";
        token.taiLongUserInfo = null;
        token.expiresAt = 1712907520;
        token.allowedMerchantIds = new ArrayList<>();
        try {
            DomainServices.auth("KXY", token, null);
        } catch (Exception e) {
            Assert.assertEquals("未找到开薪易登录信息", e.getMessage());
        }
    }

    @Test
    void testValidDataPermissions() {
        List<String> allowedMerchantId = Arrays.asList("1", "2", "3");
        String merchantId = "1";
        DomainServices.validDataPermissions(allowedMerchantId, merchantId);
        Assert.assertEquals("1", merchantId);

        List<String> allowedMerchantId2 = Arrays.asList("all");
        DomainServices.validDataPermissions(allowedMerchantId2, merchantId);
        Assert.assertEquals("1", merchantId);

        List<String> allowedMerchantId3 = Arrays.asList("2", "3");
        try {
            DomainServices.validDataPermissions(allowedMerchantId3, merchantId);
        } catch (Exception e) {
            Assert.assertEquals("当前用户无权限操作该商户数据", e.getMessage());
        }
    }

}
