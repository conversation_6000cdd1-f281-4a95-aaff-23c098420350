package com.olading.kxy.admin.domain.model;


import com.olading.kxy.admin.application.commandResult.ChangeBaseInsuredDetailCommand;
import com.olading.kxy.admin.application.commandResult.UpdateBaseInsuredProjectCommand;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class BaseInsuredProjectTest {
    @Test
    void TestChangeDetail() {

        //获取请求参数
        TestBaseSuite.CommandsAndResults<ChangeBaseInsuredDetailCommand> commandsAndResults =
                TestBaseSuite.loadCommandsAndResultsFromFolder(
                        "src/test/resources/testdata/baseInsuredProject.changeDetail",
                        ChangeBaseInsuredDetailCommand.class);

        for (int i = 0; i < commandsAndResults.commands.size(); i++) {
            ChangeBaseInsuredDetailCommand c = commandsAndResults.commands.get(i);
            BaseInsuredProject baseInsuredProject = TestBaseSuite.loadObjectFromFile(
                    "src/test/resources/testdata/model/baseInsuredProject.json", BaseInsuredProject.class);
            try {
                baseInsuredProject.changeDetail(c);
                TestBaseSuite.writeObjectToFile(commandsAndResults.resultFilepaths.get(i), baseInsuredProject);
                TestBaseSuite.assetObjectEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i), baseInsuredProject);
            } catch (Exception e) {
                TestBaseSuite.writeStringToFile(commandsAndResults.resultFilepaths.get(i), e.getMessage());
                Assert.assertEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i).trim(), e.getMessage());
            }
        }

    }

    @Test
    void TestAppend() {
        //获取请求参数
        TestBaseSuite.CommandsAndResults<BaseInsuredDetail> commandsAndResults =
                TestBaseSuite.loadCommandsAndResultsFromFolder(
                        "src/test/resources/testdata/baseInsuredProject.append",
                        BaseInsuredDetail.class);

        for (int i = 0; i < commandsAndResults.commands.size(); i++) {
            BaseInsuredDetail c = commandsAndResults.commands.get(i);
            BaseInsuredProject baseInsuredProject = TestBaseSuite.loadObjectFromFile(
                    "src/test/resources/testdata/model/baseInsuredProject.json", BaseInsuredProject.class);

            try {
                baseInsuredProject.append(c);
                TestBaseSuite.writeObjectToFile(commandsAndResults.resultFilepaths.get(i), baseInsuredProject);
                TestBaseSuite.assetObjectEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i), baseInsuredProject);
            } catch (Exception e) {
                TestBaseSuite.writeStringToFile(commandsAndResults.resultFilepaths.get(i), e.getMessage());
                Assert.assertEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i).trim(), e.getMessage());
            }
        }
    }

    @Test
    void TestDepend() {
        //获取请求参数
        TestBaseSuite.CommandsAndResults<BaseInsuredDetail> commandsAndResults =
                TestBaseSuite.loadCommandsAndResultsFromFolder(
                        "src/test/resources/testdata/baseInsuredProject.depend",
                        BaseInsuredDetail.class);

        for (int i = 0; i < commandsAndResults.commands.size(); i++) {
            BaseInsuredDetail c = commandsAndResults.commands.get(i);
            BaseInsuredProject baseInsuredProject = TestBaseSuite.loadObjectFromFile(
                    "src/test/resources/testdata/model/baseInsuredProject.json", BaseInsuredProject.class);

            try {
                baseInsuredProject.depend(c);
                TestBaseSuite.writeObjectToFile(commandsAndResults.resultFilepaths.get(i), baseInsuredProject);
                TestBaseSuite.assetObjectEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i), baseInsuredProject);
            } catch (Exception e) {
                TestBaseSuite.writeStringToFile(commandsAndResults.resultFilepaths.get(i), e.getMessage());
                Assert.assertEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i).trim(), e.getMessage());
            }
        }
    }

    @Test
    void TestUpdate() {
        //获取请求参数
        TestBaseSuite.CommandsAndResults<UpdateBaseInsuredProjectCommand> commandsAndResults =
                TestBaseSuite.loadCommandsAndResultsFromFolder(
                        "src/test/resources/testdata/baseInsuredProject.update",
                        UpdateBaseInsuredProjectCommand.class);

        for (int i = 0; i < commandsAndResults.commands.size(); i++) {
            UpdateBaseInsuredProjectCommand c = commandsAndResults.commands.get(i);
            BaseInsuredProject baseInsuredProject = TestBaseSuite.loadObjectFromFile(
                    "src/test/resources/testdata/model/baseInsuredProject.json", BaseInsuredProject.class);

            try {
                baseInsuredProject.update(c);
                baseInsuredProject.updateTime = "0";
                TestBaseSuite.writeObjectToFile(commandsAndResults.resultFilepaths.get(i), baseInsuredProject);
                TestBaseSuite.assetObjectEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i), baseInsuredProject);
            } catch (Exception e) {
                TestBaseSuite.writeStringToFile(commandsAndResults.resultFilepaths.get(i), e.getMessage());
                Assert.assertEquals(commandsAndResults.resultFilepaths.get(i), commandsAndResults.results.get(i).trim(), e.getMessage());
            }
        }
    }

}


