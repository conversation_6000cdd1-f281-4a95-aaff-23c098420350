package com.olading.kxy.admin.domain.model;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.List;

//数据驱动测试
public class MerchantServicesTest {

    private List<String> allowedBusinessIds;
    private List<String> openedBusinessIds;
    private String baseServiceBusinessId;
    private List<String> allowedAdvancedBusinessIds;
    private List<String> openedAdvancedBusinessIds;

    @Test
    void TestUpdateBusiness() {
        allowedBusinessIds= Arrays.asList("1","2","3","4");
        openedBusinessIds= Arrays.asList("1","2","3","4");
        baseServiceBusinessId="1";
        allowedAdvancedBusinessIds= Arrays.asList("1","2","3","4");
        openedAdvancedBusinessIds= Arrays.asList("1","2","3","4");
        Merchant merchant=new Merchant();
        merchant.updateBusiness(allowedBusinessIds,openedBusinessIds,baseServiceBusinessId,allowedAdvancedBusinessIds,openedAdvancedBusinessIds);
        Assert.assertEquals("1",baseServiceBusinessId);

        try {
            String baseServiceBusinessId = "50";
            merchant.updateBusiness(allowedBusinessIds,openedBusinessIds,baseServiceBusinessId,allowedAdvancedBusinessIds,openedAdvancedBusinessIds);
        }catch (Exception e){
            Assert.assertEquals("基础服务必须开启",e.getMessage());

        }

    }

    @Test
    void TestUpdateBusinessErrApplicationId() {
        allowedBusinessIds= Arrays.asList("1","2","3","4");
        openedBusinessIds= Arrays.asList("1","5","3","4");
        baseServiceBusinessId="1";
        allowedAdvancedBusinessIds= Arrays.asList("1","2","3","4");
        openedAdvancedBusinessIds= Arrays.asList("1","2","3","4");

        try {
            Merchant merchant=new Merchant();
            merchant.updateBusiness(allowedBusinessIds,openedBusinessIds,baseServiceBusinessId,allowedAdvancedBusinessIds,openedAdvancedBusinessIds);
        }catch (Exception e){
            Assert.assertEquals("未知应用ID",e.getMessage());

        }

    }

    @Test
    void TestUpdateBusinessErrAdvancesApplicationId() {
        allowedBusinessIds= Arrays.asList("1","2","3","4");
        openedBusinessIds= Arrays.asList("1","2","3","4");
        baseServiceBusinessId="1";
        allowedAdvancedBusinessIds= Arrays.asList("1","2","3","4");
        openedAdvancedBusinessIds= Arrays.asList("1","5","3","4");

        try {
            Merchant merchant=new Merchant();
            merchant.updateBusiness(allowedBusinessIds,openedBusinessIds,baseServiceBusinessId,allowedAdvancedBusinessIds,openedAdvancedBusinessIds);
        }catch (Exception e){
            Assert.assertEquals("未知高级应用ID",e.getMessage());

        }

    }


}
