package com.olading.kxy.admin.domain.model;

import com.olading.kxy.admin.application.commandResult.UpdateOpenedBusinessCommand;

import java.util.List;

public interface MerchantRepository {
    void changeBusinesses(Merchant merchant);

    void changeBaseInfo(Merchant merchant);

    /**
     * 只修改商户的审核状态
     * @param merchant 商户对象
     */
    void changeAudit(Merchant merchant);

    Merchant findBy(String merchantId);

    List<String> getAllowedMerchantIds(String blngInstNo, String binInstNo);

    List<String> allowedBusinessIds();

    List<String> allowedAdvancedBusinessIds();

    String baseServiceBusinessId();

}
