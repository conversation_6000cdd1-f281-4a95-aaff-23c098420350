package com.olading.kxy.admin.application;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lanmaoly.cloud.salary.boss.service.SalaryMerchantRpcService;
import com.lanmaoly.cloud.salary.boss.service.UpdateTaxSubAuthStatusRpcReq;
import com.olading.kxy.admin.application.commandResult.*;
import com.olading.kxy.admin.domain.model.*;
import com.olading.kxy.admin.port.db.mysql.TokenRepository;
import com.olading.kxy.admin.port.db.mysql.LegalRepository;
import com.olading.kxy.admin.port.db.mysql.RoleRepository;
import com.olading.kxy.admin.port.db.mysql.UserRepository;
import com.olading.kxy.admin.port.db.mysql.OperateLogRepository;
import com.olading.kxy.admin.port.log.Logger;
import com.olading.kxy.admin.port.tailong.TaiLongService;
import com.olading.platform.api.rpc.PlatformRpcService;
import com.olading.platform.api.rpc.SaveUserRequest;
import com.olading.platform.api.rpc.SaveUserResponse;
import com.olading.platform.api.rpc.UserVo;
import com.olading.tl.openplatform.api.rpc.CommonRespHeader;
import com.olading.tl.openplatform.api.rpc.SendSmsReq;
import com.olading.tl.openplatform.api.rpc.SendSmsResp;
import com.olading.tl.openplatform.api.rpc.SmsUserDetail;
import com.olading.tl.openplatform.api.rpc.TLOpenPlatformRpcService;
import com.olading.tl.openplatform.api.rpc.TextMsgPushReq;
import com.olading.tl.openplatform.api.rpc.TextMsgPushResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;


@RestController
public class AdminService implements ErrorController {
    @Value("${app.loginDuration}")
    private long loginDuration;

    @Autowired
    public TaiLongService tailongService;
    @Autowired
    public TokenRepository tokenRepository;
    @Autowired
    public MerchantRepository merchantRepository;
    @Autowired
    public MerchantStatisticsQueryService merchantStatisticsQueryService;
    @Autowired
    public MerchantQueryService merchantQueryService;
    @Autowired
    public LegalRepository legalRepository;
    @Autowired
    public LegalStatisticsQueryService legalStatisticsQueryService;
    @Autowired
    public UserRepository userRepository;
    @Autowired
    public UserQueryService userQueryService;
    @Autowired
    public RoleRepository roleRepository;
    @Autowired
    public MerchantUserQueryService merchantUserQueryService;
    @Autowired
    public RoleQueryService roleQueryService;
    @Autowired
    private LegalQueryService listLegalQueryService;
    @Autowired
    public OperateLogRepository operateLogRepository;
    @Autowired
    public OperateLogQueryService operateLogQueryService;

    @Autowired
    private InsurancesQueryService insurancesQueryService;
    @Autowired
    private CityQueryService cityQueryService;
    @Autowired
    private BaseInsuredProjectRepository baseInsuredProjectRepository;
    @Autowired
    private SalaryMerchantRpcService salaryMerchantRpcService;
    @Autowired
    private PlatformRpcService platformRpcService;
    @Autowired
    private TLOpenPlatformRpcService tlOpenPlatformRpcService;

    @PostMapping("/login")
    public LoginResult login(@RequestBody LoginCommand command) throws JsonProcessingException {
        if (command.accessKey.isEmpty()) {
            throw new IllegalArgumentException("未传入合适的accessKey");
        }
        if (tailongService == null) {
            throw new IllegalArgumentException("未传入合适的tailongService");
        }


        Logger.info("access key: " + command.accessKey);

        TaiLongUserInfo taiLongUserInfo = null;
        List<String> allowedMerchantIds = new ArrayList<>(Collections.singletonList("all"));

        taiLongUserInfo = tailongService.getUserInfoBy(command.accessKey);
        allowedMerchantIds = merchantRepository.getAllowedMerchantIds(taiLongUserInfo.blngInstNo, taiLongUserInfo.binInstNo);

        String tokenId = UUID.randomUUID().toString();
        Token token = DomainServices.login(tokenId, loginDuration, allowedMerchantIds, taiLongUserInfo);
        tokenRepository.save(token);

        LoginResult result = new LoginResult();
        result.token = token.id;

        return result;
    }

    @PostMapping("/logout")
    public SuccessResult logout(@RequestBody LogoutCommand command) {
        if (command.token.isEmpty()) {
            throw new IllegalArgumentException("token不能为空");
        }

        String error = DomainServices.logout(command.token);
        if (!error.isEmpty()) {
            throw new BusinessException(error);

        }

        tokenRepository.delete(command.token);

        return new SuccessResult();
    }

    @PostMapping("/listPolicyGroups")
    public ListPolicyGroupsResult listPolicyGroups(@RequestBody ListPolicyGroupsCommand command) throws JsonProcessingException {
        ListPolicyGroupsResult r = new ListPolicyGroupsResult();
        r.groups = Policy.All.getAllPolicies().groups;
        return r;
    }

    @PostMapping("/createUser")
    public CreateUserResult createUser(@RequestBody CreateUserCommand command) throws JsonProcessingException {
        if (command.user == null) {
            throw new IllegalArgumentException("user不能为空");
        }

        boolean existed = userRepository.existed(command.user.name);
        //这里无需对roleId有效性进行处理，1 这里无需处理越权,因为大家都是一套角色 2 因为获取角色的时候，不存在的roleId自然拿不出来
        //将工号直接当作id，避免重复性讨论
        User user = new User(UUID.randomUUID().toString(), command.user.name, command.user.fullName, command.user.roleIds, existed);
        userRepository.save(user);

        CreateUserResult r = new CreateUserResult();
        r.userId = user.id;
        return r;
    }

    @PostMapping("/updateUser")
    public SuccessResult updateUser(@RequestBody UpdateUserCommand command) throws JsonProcessingException {
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (command.user == null) {
            throw new IllegalArgumentException("user不能为空");
        }

        User user = userRepository.load(command.userId);
        if (user == null) {
            throw new IllegalArgumentException("找不到对应的用户");
        }

        user.update(command.user);
        userRepository.update(user);

        return new SuccessResult();
    }


    @PostMapping("/forbidUser")
    public SuccessResult forbidUser(@RequestBody ForbidUserCommand command) throws JsonProcessingException {
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        User user = userRepository.load(command.userId);
        if (user == null) {
            throw new IllegalArgumentException("找不到对应的用户");
        }

        user.forbid();
        userRepository.update(user);

        return new SuccessResult();
    }

    @PostMapping("/allowUser")
    public SuccessResult allowUser(@RequestBody AllowUserCommand command) throws JsonProcessingException {
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        User user = userRepository.load(command.userId);
        if (user == null) {
            throw new IllegalArgumentException("找不到对应的用户");
        }

        user.allow();
        userRepository.update(user);

        return new SuccessResult();
    }

    @PostMapping("/deleteUser")
    public SuccessResult deleteUser(@RequestBody DeleteUserCommand command) throws JsonProcessingException {
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        User user = userRepository.load(command.userId);
        if (user == null) {
            throw new IllegalArgumentException("找不到对应的用户");
        }

        user.delete();
        userRepository.delete(command.userId);
        return new SuccessResult();
    }

    @PostMapping("/listUsers")
    public ListUsersResult listUsers(@RequestBody ListUsersCommand command) throws JsonProcessingException {
        return userQueryService.find(command);
    }

    @PostMapping("/detailUser")
    public DetailUserResult detailUser(@RequestBody DetailUserCommand command) throws JsonProcessingException {
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        User user = userQueryService.findByUserId(command.userId);

        DetailUserResult r = new DetailUserResult();
        r.user = user;

        return r;
    }

    @PostMapping("/listMerchants")
    public ListMerchantsResult listMerchants(@RequestBody ListMerchantsCommand command) throws ParseException {
        return merchantQueryService.find(command);
    }

    @PostMapping("/merchantExport")
    public void merchantExport(@RequestBody MerchantExportCommand merchantExportCommand, HttpServletResponse response) throws ParseException {
        merchantQueryService.merchantExport(merchantExportCommand,response);
    }

    @PostMapping("/createRole")
    public CreateRoleResult createRole(@RequestBody CreateRoleCommand command) throws JsonProcessingException {
        if (command.role == null) {
            throw new IllegalArgumentException("role不能为空");
        }
        boolean exsited = roleRepository.existed(command.role.name);
        Role role = new Role(UUID.randomUUID().toString(), command.role.name, command.role.description, command.role.policies, exsited);

        roleRepository.save(role);

        CreateRoleResult r = new CreateRoleResult();
        r.roleId = role.id;
        return r;
    }

    @PostMapping("/updateRole")
    public SuccessResult updateRole(@RequestBody UpdateRoleCommand command) throws JsonProcessingException {
        if (command.roleId == null || command.roleId.isEmpty()) {
            throw new IllegalArgumentException("roleId不能为空");
        }
        if (command.role == null) {
            throw new IllegalArgumentException("role不能为空");
        }

        Role role = roleRepository.load(command.roleId);
        if (role == null) {
            throw new IllegalArgumentException("找不到对应的角色");
        }

        role.update(command.role);
        roleRepository.update(role);

        return new SuccessResult();
    }

    @PostMapping("/deleteRole")
    public SuccessResult deleteRole(@RequestBody DeleteRoleCommand command) throws JsonProcessingException {
        if (command.roleId == null || command.roleId.isEmpty()) {
            throw new IllegalArgumentException("roleId不能为空");
        }

        Role role = roleRepository.load(command.roleId);
        if (role == null) {
            throw new IllegalArgumentException("找不到对应的角色");
        }
        role.delete();

        roleRepository.delete(role.id);

        return new SuccessResult();
    }

    @PostMapping("/listRoles")
    public ListRolesResult listRoles(@RequestBody ListRolesCommand command) {
        return roleQueryService.find(command);
    }

    @PostMapping("/detailRole")
    public DetailRoleResult detailRole(@RequestBody DetailRoleCommand command) throws JsonProcessingException {
        return roleQueryService.detail(command);
    }

    @PostMapping("/listBusinesses")
    public ListBusinessesResult listBusiness(@RequestBody ListBusinessesCommand command) {
        return merchantQueryService.find(command);
    }

    @PostMapping("/detailMerchant")
    public DetailMerchantResult detailMerchant(@RequestBody DetailMerchantCommand command) {
        return merchantQueryService.findBy(command);
    }

    @PostMapping("/listBlngInsts")
    public ListBlngInstsResult listBlngInsts(@RequestBody ListBlngInstsCommand command) {
        return merchantQueryService.findBlngInsts(command);
    }

    @PostMapping("/listBinInsts")
    public ListBinInstsResult listBinInsts(@RequestBody ListBinInstsCommand command) {
        return merchantQueryService.findBinInsts(command);
    }

    @PostMapping("/listMerchantStatistics")
    public ListMerchantStatisticsResult listMerchantStatistics(@RequestBody ListMerchantStatisticsCommand command) {
        return merchantStatisticsQueryService.find(command);
    }

    @PostMapping("/listLegalStatistics")
    public ListLegalStatisticsResult listLegalStatistics(@RequestBody ListLegalStatisticsCommand command) {
        return legalStatisticsQueryService.find(command);
    }

    @PostMapping("/updateOpenedBusiness")
    public SuccessResult updateOpenedBusiness(@RequestBody UpdateOpenedBusinessCommand command) {
        Helper.validWriteDataPermissions(command.merchantId);

        Merchant merchant = merchantRepository.findBy(command.merchantId);

        List<String> allowedBusinessIds = merchantRepository.allowedBusinessIds();
        List<String> allowedAdvancedBusinessIds = merchantRepository.allowedAdvancedBusinessIds();
        String baseServiceBusinessId = merchantRepository.baseServiceBusinessId();
        merchant.updateBusiness(allowedBusinessIds, command.businessIds, baseServiceBusinessId,
                allowedAdvancedBusinessIds, command.advancedBusinessIds);

        merchantRepository.changeBusinesses(merchant);

        return new SuccessResult();
    }

    @PostMapping("/changeMerchantBaseInfo")
    public SuccessResult changeMerchantBaseInfo(@RequestBody ChangeMerchantBaseInfoCommand command) {
        if (command.merchantId.isEmpty()) {
            throw new IllegalArgumentException("merchantId不能为空");
        }

        Helper.validWriteDataPermissions(command.merchantId);

        // 获取商户对象
        Merchant merchant = merchantRepository.findBy(command.merchantId);

        // 只更新ChangeMerchantBaseInfo中约定的字段
        merchant.shortName = command.shortName;
        merchant.industry = command.industry;
        merchant.contactName = command.contactName;
        merchant.contactPhone = command.contactPhone;

        // 保存到数据库
        merchantRepository.changeBaseInfo(merchant);

        return new SuccessResult();
    }

    @PostMapping("/detailLoggedInUser")
    public DetailLoggedInUserResult detailLoggedInUser(@RequestBody DetailLoggedInUserCommand command) {
        return userQueryService.detailLoggedInUser(command);
    }

    @PostMapping("/listLegals")
    public ListLegalsResult listLegals(@RequestBody ListLegalsCommand command) {
        return listLegalQueryService.find(command);
    }

    @PostMapping("/detailLegal")
    public DetailLegalResult detailLegal(@RequestBody DetailLegalCommand command) {
        return listLegalQueryService.findBy(command);
    }

    @PostMapping("/listCities")
    public ListCitiesResult listCities(@RequestBody(required = false) ListCitiesCommand command) {
        return cityQueryService.find(command);
    }

    @PostMapping("/listMerchantUsers")
    public ListMerchantUsersResult listMerchantUsers(@RequestBody ListMerchantUsersCommand command) {
        return merchantUserQueryService.find(command);
    }

    @PostMapping("/markMerchantUserAsVerified")
    public SuccessResult markAsMerchantUserVerified(@RequestBody MarkMerchantUserAsVerifiedCommand command) {
        // 参数检查
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        try {
            // 尝试将userId转换为数字，确保是有效的ID
            long userId = Long.parseLong(command.userId);

            // 创建用户对象并设置已验证状态
            UserVo userVo = new UserVo();
            userVo.setId(userId);
            userVo.setIsAuth(true);

            // 调用平台服务更新用户状态
            SaveUserRequest request = new SaveUserRequest();
            request.setUser(userVo);
            SaveUserResponse response = platformRpcService.saveUser(request);


            return new SuccessResult();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("userId格式不正确，应为数字");
        } catch (Exception e) {
            Logger.error(e.getMessage(), "标记用户验证状态失败");
            throw new BusinessException("标记用户验证状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/markMerchantUserAsUnVerified")
    public SuccessResult markAsMerchantUserUnVerified(@RequestBody MarkMerchantUserAsUnVerifiedCommand command) {
        // 参数检查
        if (command.userId == null || command.userId.isEmpty()) {
            throw new IllegalArgumentException("userId不能为空");
        }

        try {
            // 尝试将userId转换为数字，确保是有效的ID
            long userId = Long.parseLong(command.userId);

            // 创建用户对象并设置未验证状态
            UserVo userVo = new UserVo();
            userVo.setId(userId);
            userVo.setIsAuth(false);

            // 调用平台服务更新用户状态
            SaveUserRequest request = new SaveUserRequest();
            request.setUser(userVo);
            SaveUserResponse response = platformRpcService.saveUser(request);

            return new SuccessResult();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("userId格式不正确，应为数字");
        } catch (Exception e) {
            Logger.error(e.getMessage(), "标记用户为未验证状态失败");
            throw new BusinessException("标记用户为未验证状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/approvalLegalContractAuthorization")
    public SuccessResult approvalLegalContractAuthorization(@RequestBody ApprovalLegalContractAuthorizationCommand command) {
        if (command.merchantId.isEmpty()) {
            throw new IllegalArgumentException("merchantId不能为空");
        }
        if (command.taxPayerNo.isEmpty()) {
            throw new IllegalArgumentException("taxPayerNo不能为空");
        }

        // 创建RPC请求
        UpdateTaxSubAuthStatusRpcReq req = new UpdateTaxSubAuthStatusRpcReq();
        req.setCompId(Long.parseLong(command.merchantId));
        req.setTaxPayerNo(command.taxPayerNo);
        req.setContractAuthStatus("SUCCESS"); // 设置为通过状态

        // 调用RPC服务
        salaryMerchantRpcService.updateTaxSubAuthStatusRpc(req);

        return new SuccessResult();
    }

    @PostMapping("/rejectLegalContractAuthorization")
    public SuccessResult rejectLegalContractAuthorization(@RequestBody RejectLegalContractAuthorizationCommand command) {
        if (command.merchantId.isEmpty()) {
            throw new IllegalArgumentException("merchantId不能为空");
        }
        if (command.taxPayerNo.isEmpty()) {
            throw new IllegalArgumentException("taxPayerNo不能为空");
        }

        // 创建RPC请求
        UpdateTaxSubAuthStatusRpcReq req = new UpdateTaxSubAuthStatusRpcReq();
        req.setCompId(Long.parseLong(command.merchantId));
        req.setTaxPayerNo(command.taxPayerNo);
        req.setContractAuthStatus("FAIL"); // 设置为拒绝状态
        req.setContractAuthFailReason(command.reason); // 设置拒绝原因

        // 调用RPC服务
        salaryMerchantRpcService.updateTaxSubAuthStatusRpc(req);

        return new SuccessResult();
    }

    @PostMapping("/approvalMerchant")
    public SuccessResult approvalMerchant(@RequestBody ApprovalMerchantCommand command) {
        if (command.merchantId.isEmpty()) {
            throw new IllegalArgumentException("merchantId不能为空");
        }

        Helper.validWriteDataPermissions(command.merchantId);

        // 获取商户对象
        Merchant merchant = merchantRepository.findBy(command.merchantId);

        // 审批通过商户
        merchant.approve();

        // 保存到数据库
        merchantRepository.changeAudit(merchant);

        Map<String, String> msgContext = new HashMap<>();
        msgContext.put("companyName", merchant.name);
        // 发送短信通知
        sendSMSToOpenPlatform(merchant.submitUserCellPhone, msgContext, "lxxgj005");
        pushApprovalPaoPaoMessage(merchant.name, merchant.accountManagerCode);
        return new SuccessResult();
    }

    @PostMapping("/rejectMerchant")
    public SuccessResult rejectMerchant(@RequestBody RejectMerchantCommand command) {
        if (command.merchantId.isEmpty()) {
            throw new IllegalArgumentException("merchantId不能为空");
        }

        if (command.reason != null && command.reason.length() > 100) {
            throw new IllegalArgumentException("驳回原因不可以超过100个字符");
        }

        Helper.validWriteDataPermissions(command.merchantId);

        // 获取商户对象
        Merchant merchant = merchantRepository.findBy(command.merchantId);

        // 拒绝商户
        merchant.reject(command.reason);

        // 保存到数据库
        merchantRepository.changeAudit(merchant);

        Map<String, String> msgContext = new HashMap<>();
        msgContext.put("companyName", merchant.name);
        msgContext.put("content", org.apache.commons.lang3.StringUtils.isBlank(command.reason) ? "无" : command.reason);
        // 发送短信通知
        sendSMSToOpenPlatform(merchant.submitUserCellPhone, msgContext, "lxxgj006");
        pushRejectPaoPaoMessage(merchant.name, org.apache.commons.lang3.StringUtils.isBlank(command.reason) ? "无" : command.reason, merchant.submitUserCellPhone, merchant.accountManagerCode);
        return new SuccessResult();
    }

    @PostMapping("/listBaseInsurances")
    public ListBaseInsuredProjectsResult listBaseInsurances(@RequestBody ListBaseInsuredProjectsCommand command) {
        return insurancesQueryService.findBy(command);
    }

    @PostMapping("/detailBaseInsuredProject")
    public DetailBaseInsuredProjectResult detailBaseInsuredProject(@RequestBody DetailBaseInsuredProjectCommand command) {
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(command.id);
        if (baseInsuredProject == null) {
            throw new BusinessException("无法找到对应的参保方案");
        }

        DetailBaseInsuredProjectResult result = new DetailBaseInsuredProjectResult();
        result.baseInsuredProject = baseInsuredProject;

        return result;
    }

    @PostMapping("/deleteBaseInsuredProject")
    @Transactional(rollbackFor = Exception.class)
    public SuccessResult deleteBaseInsuredProject(@RequestBody DeleteBaseInsuredProjectCommand command) {
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(command.id);
        if (baseInsuredProject == null) {
            throw new BusinessException("无法找到对应的参保方案");
        }

        baseInsuredProjectRepository.delete(baseInsuredProject.id);

        return new SuccessResult();
    }

    @PostMapping("/deleteBaseInsuranceDetail")
    public SuccessResult deleteBaseInsuranceDetail(@RequestBody DeleteBaseInsuredDetailCommand command) {
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(command.projectId);
        BaseInsuredDetail baseInsuredDetail = new BaseInsuredDetail();
        baseInsuredDetail.id = command.id;
        baseInsuredProject.depend(baseInsuredDetail);
        baseInsuredProjectRepository.update(baseInsuredProject);

        return new SuccessResult();
    }

    @PostMapping("/changeBaseInsuredDetail")
    @Transactional(rollbackFor = Exception.class)
    public SuccessResult changeBaseInsuredDetail(@RequestBody ChangeBaseInsuredDetailCommand command) {
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(command.projectId);
        if (baseInsuredProject == null) {
            throw new BusinessException("找不到对应的参保方案");
        }
        baseInsuredProject.changeDetail(command);

        baseInsuredProjectRepository.update(baseInsuredProject);

        return new SuccessResult();
    }

    @PostMapping("/updateBaseInsuredProject")
    @Transactional(rollbackFor = Exception.class)
    public SuccessResult updateBaseInsuredProject(@RequestBody UpdateBaseInsuredProjectCommand command) {
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(command.id);
        if (baseInsuredProject == null) {
            throw new BusinessException("找不到对应的参保方案");
        }

        baseInsuredProject.update(command);

        baseInsuredProjectRepository.update(baseInsuredProject);

        return new SuccessResult();
    }

    @PostMapping("/createBaseInsuredProject")
    public CreateBaseInsuredProjectResult createBaseInsuredProject(@RequestBody CreateBaseInsuredProjectCommand command) {
        String id = baseInsuredProjectRepository.nextIdentity();
        BaseInsuredProject baseInsuredProject = new BaseInsuredProject(id, command);

        baseInsuredProjectRepository.save(baseInsuredProject);

        CreateBaseInsuredProjectResult result = new CreateBaseInsuredProjectResult();
        result.id = id;

        return result;
    }

    @PostMapping("/appendBaseInsuredDetail")
    public SuccessResult appendBaseInsuredDetail(@RequestBody AppendBaseInsuredDetailCommand command) {
        String projectId = command.projectId;
        BaseInsuredProject baseInsuredProject = baseInsuredProjectRepository.findBy(projectId);
        if (baseInsuredProject == null) {
            throw new IllegalArgumentException("找不到对应的参保方案");
        }

        String baseInsuredDetailID = baseInsuredProjectRepository.nextBaseInsuredDetailIdentity();
        BaseInsuredDetail baseInsuredDetail = new BaseInsuredDetail(baseInsuredDetailID, command);

        baseInsuredProject.append(baseInsuredDetail);

        baseInsuredProjectRepository.update(baseInsuredProject);

        return new SuccessResult();
    }

    private void sendSMSToOpenPlatform(String mobile, Map<String, String> msgContext, String templateCode) {
        List<SmsUserDetail> smsUserDetails = new ArrayList<>();
        SmsUserDetail smsUserDetail = new SmsUserDetail();
        smsUserDetail.setMobile(mobile);
        smsUserDetails.add(smsUserDetail);

        SendSmsReq sendSmsReq = new SendSmsReq();
        sendSmsReq.setMsgContext(msgContext);
        sendSmsReq.setSmsUserDetail(smsUserDetails);
        sendSmsReq.setTemplateCode(templateCode);
        SendSmsResp sendSmsResp = tlOpenPlatformRpcService.sendSms(sendSmsReq);

        if (sendSmsResp.getRspData() != null && sendSmsResp.getRspData().getHead() != null){
            if (!"000000".equals(sendSmsResp.getRspData().getHead().getErrorCode())){
                throw new RuntimeException("开放平台短信发送失败：" + sendSmsResp.getRspData().getHead().getErrorMsg());
            }
        }
    }

    private void pushRejectPaoPaoMessage(String merchantName, String rejectReason, String mobile, String accountManagerCode) {
        String pushTemplate = MessageTemplateConstant.PAO_PAO_CUSTOMER_APPLY_REJECT;
        try {
            String formatSendMessage = String.format(pushTemplate, merchantName, rejectReason, mobile);
            TextMsgPushReq textMsgPushReq = new TextMsgPushReq();
            textMsgPushReq.setTotalCount("1");
            String[] split = formatSendMessage.split(MessageTemplateConstant.SEPARATOR);
            textMsgPushReq.setEmployeeId(accountManagerCode);
            textMsgPushReq.setTitle(split[0]);
            textMsgPushReq.setPushAbstract(split[1]);
            if (StringUtils.isNotBlank(textMsgPushReq.getEmployeeId())) {
                TextMsgPushResp textMsgPushResp = tlOpenPlatformRpcService.textMsgPush(textMsgPushReq);
                CommonRespHeader head = textMsgPushResp.getRspData().getHead();
                if (!Constants.TL_OPEN_PLATFORM_SUCCESS_CODE.equals(head.getErrorCode())) {
                    throw new BusinessException(head.getErrorMsg());
                }
            }
        } catch (Exception e) {
            Logger.error(e);
        }
    }

    private void pushApprovalPaoPaoMessage(String merchantName, String accountManagerCode) {
        String pushTemplate = MessageTemplateConstant.PAO_PAO_CUSTOMER_APPLY_PASS;
        try {
            String formatSendMessage = String.format(pushTemplate, merchantName);
            TextMsgPushReq textMsgPushReq = new TextMsgPushReq();
            textMsgPushReq.setTotalCount("1");
            String[] split = formatSendMessage.split(MessageTemplateConstant.SEPARATOR);
            textMsgPushReq.setEmployeeId(accountManagerCode);
            textMsgPushReq.setTitle(split[0]);
            textMsgPushReq.setPushAbstract(split[1]);
            if (StringUtils.isNotBlank(textMsgPushReq.getEmployeeId())) {
                TextMsgPushResp textMsgPushResp = tlOpenPlatformRpcService.textMsgPush(textMsgPushReq);
                CommonRespHeader head = textMsgPushResp.getRspData().getHead();
                if (!Constants.TL_OPEN_PLATFORM_SUCCESS_CODE.equals(head.getErrorCode())) {
                    throw new BusinessException(head.getErrorMsg());
                }
            }
        } catch (Exception e) {
            Logger.error(e);
        }
    }
    @PostMapping("/listOperateLogs")
    public ListOperateLogsResult listOperateLogs(@RequestBody ListOperateLogsCommand command) {
        return operateLogQueryService.find(command);
    }
    @PostMapping("/createOperateLog")
    public SuccessResult createOperateLog(@RequestBody CreateOperateLogCommand command) {
        String id = Helper.generateUUIDV7();
        if(command.uri == null || command.uri.isEmpty()){
            return new SuccessResult();
        }
        String name = DomainServices.uriToOperateLogName(command.uri);
        if(name == null || name.isEmpty()){
            return new SuccessResult();
        }
        Logger.info("createOperateLog：" + command.uri + " userAgent:"+command.userAgent + " ip:"+command.ip+" token:"+command.token+ " result:"+command.result+ " failedReason:"+command.failedReason);
        OperateLog operateLog = new OperateLog(id, command.userAgent,command.ip,command.uri,command.method,command.token,command.result,command.failedReason);

        operateLogRepository.save(operateLog);

        return new SuccessResult();
    }
}
