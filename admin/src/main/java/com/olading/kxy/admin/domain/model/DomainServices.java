package com.olading.kxy.admin.domain.model;


import com.olading.kxy.admin.port.log.Logger;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DomainServices {
    public static Token login(String tokenId, long loginDuration, List<String> allowedMerchantIds, TaiLongUserInfo taiLongUserInfo) {
        if (taiLongUserInfo != null) {
            long now = System.currentTimeMillis();
            long expiredAt = now + loginDuration;

            return new Token(taiLongUserInfo, tokenId, expiredAt, allowedMerchantIds);
        }

        throw new BusinessException("不支持的验证类型");
    }

    public static String logout(String token) {
        Logger.info("logout" + token.substring(0, -10));
        return "";
    }

    public static String auth(String type, User user, Token token, List<String> userAllPolicies, String uri) {
        if (type.equals("TL")) {
            return authWithKXYToken(user, token, userAllPolicies, uri);
        }

        return "不支持的授权类型";
    }

    private static String authWithKXYToken(User user, Token token, List<String> userAllPolicies, String uri) {
        if (token == null) {
            return "未找到登录信息";
        }
        int now = (int) (System.currentTimeMillis() / 1000);
        if (token.expiresAt < now) {
            return "当前登录已过期";
        }
        if (user.status.equals(Constants.Forbidden)) {
            return "当前用户已被禁用";
        }
        if (userAllPolicies == null || userAllPolicies.isEmpty()) {
            return "当前用户无权限，请联系管理员进行授权";
        }
        for (String policy : userAllPolicies) {
            if (policy == null) continue;
            if (policy.isEmpty()) continue;
            if (policy.equals("*")) {
                return "";
            }
            if (policy.equals(uri)) {
                return "";
            }
        }

        return "无权限，请联系管理员进行授权";
    }

    public static void validDataPermissions(List<String> allowedMerchantId, String merchantId) {
        boolean isAllowed = false;
        for (String id : allowedMerchantId) {
            if (id.equals("all")) {
                return;
            }
            if (id.equals(merchantId)) {
                isAllowed = true;
                break;
            }
        }

        if (!isAllowed) {
            throw new BusinessException("当前用户无权限操作该商户数据");
        }
    }

    private static HashMap<String, String> uriOperateLogNameMap = new HashMap<String,String>() {{
        put("/api/platform/login","登录/注册");
        put("/api/platform/logout","退出登录");
        put("/api/platform/forgotPassword","找回密码");
        put("/api/dept/save-merchantMember","人事管理-组织管理-组织架构-添加人员");
        put("/api/dept/save-dept","人事管理-组织管理-组织架构-添加子部门");
        put("/api/dept/importDept","人事管理-组织管理-组织架构-批量导入");
        put("/api/post/add2Post","人事管理-组织管理-岗位管理-添加岗位");
        put("/api/addEmp/addEmp","人事管理-员工管理-花名册-添加员工");
        put("/api/empList/transfer-position","人事管理-员工管理-花名册-调岗");
        put("/api/entry/v1/emp/regist","人事管理-员工管理-入职管理-预入职登记");
        put("/api/entry/v1/emp/update/confirm","人事管理-员工管理-入职管理-确认到岗");
        put("/api/entry/v1/emp/update/regular","人事管理-员工管理-转正管理-办理转正");
        put("/api/resign/v1/emp/update/waitResign","人事管理-员工管理-离职管理-待离职登记");
        put("/api/resign/v1/emp/update/resign","人事管理-员工管理-离职管理-确认离职");
        put("/api/empReport/exportEmpReport","人事管理-人事报表-常用报表-导出");
        put("/api/contractRecord/newSign","人事管理-合同管理-劳动合同管理-新签");
        put("/api/contractTemplate/createTemplate","人事管理-合同管理-合同模板-新增模板");
        put("/api/group/plus/savaOrUpdateAttendGroupOverNight","人事管理-考勤管理-考勤组-新增考勤组");
        put("/api/work/plus/savaOrUpdateWorkingShiftOverNight","人事管理-考勤管理-班次管理-新增班次");
        put("/api/supplementRule/savaOrUpdateSupplementRule","人事管理-考勤管理-补卡规则-新增补卡规则");
        put("/api/overtime/savaOrUpdateOvertimeRule","人事管理-考勤管理-加班规则-新增加班规则");
        put("/api/machine/zoneAdd","人事管理-考勤管理-考勤机管理-添加考勤机");
        put("/api/leave/addLeave","人事管理-假期管理-假期类型-新增假期");
        put("/api/count/dayCount/export","人事管理-考勤报表-每日统计-导出");
        put("/api/count/monthCount/export","人事管理-考勤报表-月度统计-导出");
        put("/api/count/signTime/export","人事管理-考勤报表-打卡时间-导出");
        put("/api/count/signRecord/export","人事管理-考勤报表-原始记录-导出");
        put("/api/overtime/export","人事管理-考勤报表-加班明细-导出");
        put("/api/template/templateFieldUpdate","人事管理-人事设置-员工信息设置-添加员工设置");
        put("/api/options/saveOptions","人事管理-人事设置-常用字段设置-新增");
        put("/api/floatEmployee/increase/do","薪税管理-社保公积金-增减员-快速增减员-社保增员");
        put("/api/monthlyLedger/saveMonthlyLedger","薪税管理-社保公积金-参保阅读台账-生成阅读台账");
        put("/api/insuredProject/manage/save","薪税管理-社保公积金-参保方案-新增参保方案");
        put("/api/salary/adjust/syncEmployee","薪税管理-薪资核算-薪资档案-同步新增人员");
        put("/api/salary/adjust/confirm-salary","薪税管理-薪资核算-薪资档案-定薪");
        put("/api/attendance/plan","薪税管理-薪资核算-计薪规则-新增方案");
        put("/api/salary/initSalaryCheck","薪税管理-薪资核算-薪资核算-计算薪资");
        put("/api/salary/rule/save","薪税管理-薪资核算-薪资核算-新增工资表");
        put("/api/paySalaryApply/savePaySalaryApplyBatch","薪税管理-智能代发-代发申请-新增代发");
        put("/api/subjectAccount/savaSubjectAccount","薪税管理-智能代发-代发账户-新增账户");
        put("/api/salary/payslip/saveSalaryBatchItems","薪税管理-工资条-工资条管理-新增工资条");
        put("/api/salary/payslip/saveSettings","薪税管理-工资条-工资条设置-保存");
        put("/api/taxReport/report","薪税管理-个税申报-人员信息采集-报送");
        put("/api/taxReport/deleteTaxReportInfo","薪税管理-个税申报-人员信息采集-删除");
        put("/api/share/DeductionsDownload/batchDownloadAddition","薪税管理-个税申报-专项附加扣除-批量下载");
        put("/api/share/DeductionsDownload/bitchDownloadAdditionQuery","薪税管理-个税申报-专项附加扣除-获取反馈");
        put("/api/taxReport/otherTotalExport","薪税管理-个税申报-专项附加扣除-导出");
        put("/api/taxReport/pension/pensionDownload","薪税管理-个税申报-个人养老金-批量下载");
        put("/api/taxReport/pension/pensionDownloadQuery","薪税管理-个税申报-个人养老金-获取反馈");
        put("/api/taxReport/pension/pensionExport","薪税管理-个税申报-个人养老金-导出");
        put("/api/taxReport/cancelSubTaxReport","薪税管理-个税申报-综合所得申报-作废申报");
        put("/api/dataAuth/selectPlatformUser","薪税管理-薪酬设置-数据权限管理-用户数据权限新增");
        put("/api/dataAuth/DelPlatformUser","薪税管理-薪酬设置-数据权限管理-删除");
        put("/api/dataAuth/DataAuthUserList/export","薪税管理-薪酬设置-数据权限管理-导出当前数据");
        put("/api/dataAuth/modifyState","薪税管理-薪酬设置-数据权限管理-是否开启用户数据权限");
        put("/api/receipt/save","OA审批-审批设置-单据管理-新增单据");
        put("/api/receipt/group/save","OA审批-审批设置-单据管理-新建分组");
        put("/api/roleManagement/saveRoleInfo","企业管理-权限管理-权限管理-新增角色");
        put("/api/dept/roleBindUser","企业管理-权限管理-权限管理-授权用户");
        put("/api/taxSubject/addTaxSubject","企业管理-企业设置-法人实体-新增");
        put("/api/accessToken/add","企业管理-企业设置-数据接入-添加");
        put("/api/merchantConfig/deptConfig","企业管理-企业设置-显示设置-修改");
        put("/api/merchant/perfect_info","个人设置-我的企业-申请企业认证");
        put("/api/merchantNet/addMerchant","个人设置-我的企业-创建企业");

    }};

    public static String uriToOperateLogName(String uri){
        String operateLogName = DomainServices.uriOperateLogNameMap.get(uri);
        if(operateLogName == null || operateLogName.isEmpty()){
            return null;
        }

        return operateLogName;
    }
    public static List<String> parseTokenToMerchantAndUserID(String token) {
        if(token == null || token.isEmpty()){
            return null;
        }
        String[] split = token.split("\\.");
        if(split.length != 3){
            return null;
        }
        Base64.Decoder decoder = Base64.getDecoder();
        String decoded = new String(decoder.decode(split[1]));
        if(decoded.isEmpty()){
            return null;
        }

        Pattern pattern = Pattern.compile("\"m\":\"(\\d+)\",\"mk\":\"\\d+\".*?\"o\":\"(\\d+)\"");
        Matcher matcher = pattern.matcher(decoded);

        if (matcher.find()) {
            String mValue = matcher.group(1); // 提取 "merchant" 的值
            String oValue = matcher.group(2); // 提取 "user" 的值

            return new ArrayList<String>( ) {{
                add(mValue);
                add(oValue);
            }};
        }

        return null;
    }
}
