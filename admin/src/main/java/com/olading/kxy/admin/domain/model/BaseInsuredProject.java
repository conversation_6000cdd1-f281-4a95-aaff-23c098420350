package com.olading.kxy.admin.domain.model;


import com.olading.kxy.admin.application.commandResult.CreateBaseInsuredProjectCommand;
import com.olading.kxy.admin.application.commandResult.ChangeBaseInsuredDetailCommand;
import com.olading.kxy.admin.application.commandResult.UpdateBaseInsuredProjectCommand;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class BaseInsuredProject implements DomainModel {
    public String id;
    public String insuredName = "";
    public City city = new City();
    public String accumulationFundYn = "";
    public String createTime = "";
    public List<BaseInsuredDetail> baseInsuredDetails = new ArrayList<>();
    public String remark = "";
    public String updateTime = "";

    public BaseInsuredProject() {
    }

    public BaseInsuredProject(String id, CreateBaseInsuredProjectCommand command) {
        this.id = id;
        this.insuredName = command.baseInsuredProject.insuredName;
        this.city = command.baseInsuredProject.city;
        this.accumulationFundYn = command.baseInsuredProject.accumulationFundYn;
        this.createTime = LocalDateTime.now().toString();
        this.updateTime = LocalDateTime.now().toString();
        this.baseInsuredDetails = new ArrayList<>();
        this.remark = command.baseInsuredProject.remark;
        this.valid();
    }

    public void update(UpdateBaseInsuredProjectCommand command) {
        insuredName = command.baseInsuredProject.insuredName;
        city = command.baseInsuredProject.city;
        accumulationFundYn = command.baseInsuredProject.accumulationFundYn;
        remark = command.baseInsuredProject.remark;
        updateTime = LocalDateTime.now().toString();
        valid();
    }

    public void depend(BaseInsuredDetail baseInsuredDetail) {
        boolean existed = false;
        for (BaseInsuredDetail c : baseInsuredDetails) {
            if (c.id.equals(baseInsuredDetail.id)) {
                existed = true;
                break;
            }
        }
        if (!existed) {
            throw new BusinessException("当前参保方案不存在该条目");
        }

        this.baseInsuredDetails = this.baseInsuredDetails.stream().filter(c -> !c.id.equals(baseInsuredDetail.id)).collect(Collectors.toList());
    }


    public void append(BaseInsuredDetail baseInsuredDetail) {
        baseInsuredDetail.valid();
        if (!baseInsuredDetail.baseInsuredId.equals(id)) {
            throw new BusinessException("参保方案id不匹配");
        }

        for (BaseInsuredDetail c : baseInsuredDetails) {
            if (c.insuranceType.equals(baseInsuredDetail.insuranceType)) {
                throw new BusinessException("保险名称不能重复");
            }
        }

        Map<String, String> insuranceMappings = new HashMap<>();

        // 在这里，添加所有可接受的项目类型和保险类型的配对
        insuranceMappings.put("ENDOWMENT_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("MEDICAL_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("UNEMPLOYMENT_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("INJURY_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("BIRTH_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("SERIOUS_DISEASE_TREATMENT", "SOCIAL_INSURANCE");
        insuranceMappings.put("ACCUMULATION_FUND", "ACCUMULATION_FUND");
        insuranceMappings.put("RESIDUAL_INSURANCE_GOLD", "SOCIAL_INSURANCE");
        insuranceMappings.put("COMPENSATORY_ENDOWMENT_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("COMPENSATORY_MEDICAL_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("COMPENSATORY_UNEMPLOYMENT_INSURANCE", "SOCIAL_INSURANCE");
        insuranceMappings.put("COMPENSATORY_ACCUMULATION_FUND", "ACCUMULATION_FUND");

        String expectedInsuranceType = insuranceMappings.get(baseInsuredDetail.insuranceType);

        if (expectedInsuranceType != null && expectedInsuranceType.equals(baseInsuredDetail.insuredProjectType)) {
            baseInsuredDetails.add(baseInsuredDetail);
        } else {
            throw new IllegalArgumentException("参保类型与参保名称没有对应");
        }
    }

    public void valid() {
        Validator.validId(id);
        if (insuredName.isEmpty()) {
            throw new IllegalArgumentException("参保方案名称不能为空");
        }
        if (city.name.isEmpty() && city.code.isEmpty()) {
            throw new IllegalArgumentException("所属城市不能为空");
        }
        city.valid();
        if (accumulationFundYn.isEmpty()) {
            throw new IllegalArgumentException("公积金是否缴纳不能为空");
        }

    }

    @Override
    public String getId() {
        return id;
    }

    public void changeDetail(ChangeBaseInsuredDetailCommand command) {
        command.baseInsuredDetail.valid();
        List<BaseInsuredDetail> baseInsuredDetails = this.baseInsuredDetails.stream().filter(c -> c.id.equals(command.baseInsuredDetail.id)).collect(Collectors.toList());
        if (baseInsuredDetails.size() == 0) {
            throw new BusinessException("当前参保方案不存在该条目");
        }

        List<BaseInsuredDetail> baseInsuredDetailed = this.baseInsuredDetails.stream()
                .filter(date -> date.insuranceType.equals(command.baseInsuredDetail.insuranceType)
                        && date.insuredProjectType.equals(command.baseInsuredDetail.insuredProjectType))
                .collect(Collectors.toList());
        if (baseInsuredDetailed.size() > 1) {
            throw new BusinessException("该险种已存在");
        }
        int index = this.baseInsuredDetails.indexOf(command.baseInsuredDetail);
        this.baseInsuredDetails.set(index, command.baseInsuredDetail);
    }

}
