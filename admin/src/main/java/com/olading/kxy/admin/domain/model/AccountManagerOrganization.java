package com.olading.kxy.admin.domain.model;

import java.util.ArrayList;
import java.util.List;

public class AccountManagerOrganization implements  Model  {
    public String id = "";
    public String code = "";
    public String name = "";
    List<String> parentCodes = new ArrayList<>();
    public String status = "";
    public AccountManagerOrganization(){}
    public AccountManagerOrganization(String id,String code, String name, List<String> parentCodes){
        this.id = id;
        this.name = name;
        this.code = code;
        this.parentCodes = parentCodes;
        if(this.parentCodes == null){
            this.parentCodes = new ArrayList<>();
        }

        this.valid();
    }

    @Override
    public String getID() {
        return id;
    }

    @Override
    public void valid() {
        if (id.isEmpty()) throw new IllegalArgumentException("id不能为空");
        if (name.isEmpty()) throw new IllegalArgumentException("name不能为空");
        if (code.isEmpty()) throw new IllegalArgumentException("code不能为空");
        for(String s : parentCodes){
            if(s == null){
                throw new IllegalArgumentException("parentCodes不能为null");
            }
        }
    }
}
