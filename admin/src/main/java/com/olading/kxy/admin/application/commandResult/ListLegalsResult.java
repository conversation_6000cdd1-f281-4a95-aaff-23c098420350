package com.olading.kxy.admin.application.commandResult;

import com.olading.kxy.admin.domain.model.AccountManagerOrganization;
import com.olading.kxy.admin.domain.model.Legal;

import java.util.ArrayList;
import java.util.List;

public class ListLegalsResult {
    public List<Legal> legals;
    public Integer total = 0;
    public Integer offset = 0;
    public Integer limit = 10;
    public List<AccountManagerOrganization> accountManagerOrganizations = new ArrayList<>();
}
