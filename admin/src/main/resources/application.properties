server.port=8777
server.error.whitelabel.enabled=false
# 显示执行的SQL语句
logging.level.org.springframework.jdbc.core.JdbcTemplate=DEBUG
# 显示SQL参数值
logging.level.org.springframework.jdbc.core.StatementCreatorUtils=TRACE
# 应用配置
## 一天过期
app.loginDuration=86400000 
app.mock=true
#46
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.jackson.default-property-inclusion=non_null
#RPCConfig
app.salaryMerchantRpcAddress=************:28095
app.archiveRpcAddress=************:28002
app.outApiRpcAddress=************:30019
app.merchantService=************:28005
# 异常处理
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false
